import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import signal
from scipy.ndimage import gaussian_filter1d
import warnings
import os
warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['SimHei']  # 黑体
plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示为方块的问题

class JumpAnalyzer:
    def __init__(self, keypoints_data=None, fps=30, data_path=None):
        """
        初始化跳跃分析器

        参数:
        keypoints_data: numpy数组，形状为 (frames, keypoints, 3)
                       第三维为 [x, y, confidence]
        fps: 帧率
        data_path: Excel文件路径（可选，如果提供则从Excel加载数据）
        """
        if data_path is not None:
            # 从Excel文件加载数据
            self.keypoints_data = self._load_from_excel(data_path)
        elif keypoints_data is not None:
            self.keypoints_data = keypoints_data
        else:
            raise ValueError("必须提供 keypoints_data 或 data_path 参数")

        self.fps = fps
        self.num_frames = self.keypoints_data.shape[0]
        
        self.keypoint_indices = {
            'left_ankle': 27,    # 左踝
            'right_ankle': 28,   # 右踝
            'left_heel': 29,     # 左脚跟  
            'right_heel': 30,    # 右脚跟
            'left_toe': 31,      # 左脚尖
            'right_toe': 32,     # 右脚尖
            'left_hip': 23,      # 左髋
            'right_hip': 24,     # 右髋
            'left_knee': 25,     # 左膝
            'right_knee': 26,    # 右膝
            'left_shoulder': 11,  # 左肩
            'right_shoulder': 12, # 右肩
            'left_elbow': 13,     # 左肘
            'right_elbow': 14,    # 右肘
            'left_wrist': 15,     # 左腕
            'right_wrist': 16,   # 右腕
        }
        
        # 存储分析结果
        self.takeoff_frame = None
        self.landing_frame = None
        self.ground_baseline = None
        self.smoothed_data = None

    def _load_from_excel(self, file_path):
        """
        从Excel文件加载关键点数据

        参数:
        file_path: Excel文件路径

        返回:
        keypoints_data: numpy数组，形状为 (frames, keypoints, 3)
        """
        print(f"正在加载Excel文件: {file_path}")

        # 读取Excel文件
        df = pd.read_excel(file_path)

        # 获取帧数
        num_frames = len(df)

        # 根据列名确定关键点数量（假设有X,Y坐标对）
        coord_columns = [col for col in df.columns if '_X' in col or '_Y' in col]
        keypoint_indices = set()
        for col in coord_columns:
            if '_X' in col:
                idx = int(col.split('_')[0])
                keypoint_indices.add(idx)
            elif '_Y' in col:
                idx = int(col.split('_')[0])
                keypoint_indices.add(idx)

        num_keypoints = max(keypoint_indices) + 1 if keypoint_indices else 33

        # 初始化关键点数据数组 (frames, keypoints, 3) - [x, y, confidence]
        keypoints_data = np.zeros((num_frames, num_keypoints, 3))

        # 填充数据
        for idx in keypoint_indices:
            x_col = f"{idx}_X"
            y_col = f"{idx}_Y"

            if x_col in df.columns and y_col in df.columns:
                keypoints_data[:, idx, 0] = df[x_col].astype(float).values  # X坐标
                keypoints_data[:, idx, 1] = df[y_col].astype(float).values  # Y坐标
                keypoints_data[:, idx, 2] = 1.0  # 置信度设为1.0

        print(f"数据加载完成: {num_frames} 帧, {num_keypoints} 个关键点")
        return keypoints_data
        
    def smooth_keypoints(self, window_size=5, sigma=1.0):
        """
        对关键点坐标进行平滑处理
        
        参数:
        window_size: 滑动平均窗口大小
        sigma: 高斯滤波标准差
        """
        print("正在对关键点进行平滑处理...")
        
        smoothed = self.keypoints_data.copy()
        
        # 对每个关键点的x,y坐标进行平滑
        for kp in range(self.keypoints_data.shape[1]):
            for coord in range(2):  # x, y坐标
                # 使用高斯滤波平滑
                smoothed[:, kp, coord] = gaussian_filter1d(
                    self.keypoints_data[:, kp, coord], sigma=sigma
                )
                
                # 再使用滑动平均进一步平滑
                df = pd.DataFrame(smoothed[:, kp, coord])
                smoothed[:, kp, coord] = df.rolling(
                    window=window_size, center=True, min_periods=1
                ).mean().values.flatten()
        
        self.smoothed_data = smoothed
        print("平滑处理完成")
        
    def establish_ground_baseline(self, baseline_frames=30):
        """
        建立地面基准线
        
        参数:
        baseline_frames: 用于计算基准的帧数（起跳前的帧）
        """
        print("正在建立地面基准线...")
        
        # 获取脚部关键点
        foot_points = ['left_toe', 'right_toe', 'left_heel', 'right_heel']
        y_coords = []
        
        for point in foot_points:
            if point in self.keypoint_indices:
                idx = self.keypoint_indices[point]
                y_coords.append(self.smoothed_data[:baseline_frames, idx, 1])
        
        # 计算前几帧的脚部Y坐标均值作为地面基准
        y_coords = np.array(y_coords)
        self.ground_baseline = np.mean(y_coords)
        
        print(f"地面基准线确定: Y = {self.ground_baseline:.2f}")
        
    def detect_foot_contact(self, threshold_pixels=10, min_duration=3):
        """
        检测脚部接触状态
        
        参数:
        threshold_pixels: 离地判定阈值（像素）
        min_duration: 状态持续最小帧数
        
        返回:
        left_contact, right_contact: 左右脚接触状态数组
        """
        print("正在检测脚部接触状态...")
        
        # 获取脚尖坐标
        left_toe_y = self.smoothed_data[:, self.keypoint_indices['left_toe'], 1]
        right_toe_y = self.smoothed_data[:, self.keypoint_indices['right_toe'], 1]
        
        # 计算相对地面基准的高度（注意图像坐标系中Y值越大越靠下）
        left_height = self.ground_baseline - left_toe_y
        right_height = self.ground_baseline - right_toe_y
        
        # 初始接触判定（高度小于阈值认为接触地面）
        left_contact_raw = left_height < threshold_pixels
        right_contact_raw = right_height < threshold_pixels
        
        # 平滑接触状态，避免抖动
        left_contact = self._smooth_contact_state(left_contact_raw, min_duration)
        right_contact = self._smooth_contact_state(right_contact_raw, min_duration)
        
        # 结合速度信息辅助判定
        left_velocity = np.gradient(left_toe_y)
        right_velocity = np.gradient(right_toe_y)
        
        # 当脚部向上运动时更倾向于判定为离地
        left_contact = left_contact & (left_velocity >= -2)
        right_contact = right_contact & (right_velocity >= -2)
        
        return left_contact, right_contact
    
    def _smooth_contact_state(self, contact_raw, min_duration):
        """平滑接触状态，消除短暂的状态切换"""
        contact = contact_raw.copy()
        
        # 消除短暂的离地状态
        for i in range(len(contact) - min_duration):
            if not contact[i] and all(contact[i+1:i+min_duration+1]):
                contact[i] = True
                
        # 消除短暂的接触状态
        for i in range(len(contact) - min_duration):
            if contact[i] and not any(contact[i+1:i+min_duration+1]):
                contact[i] = False
                
        return contact
    
    def detect_takeoff_landing(self, left_contact, right_contact):
        """
        检测起跳和落地帧
        
        参数:
        left_contact, right_contact: 左右脚接触状态
        """
        print("正在检测起跳和落地时刻...")
        
        # 双脚状态：两只脚都接触地面为True
        both_feet_contact = left_contact & right_contact
        
        # 找到起跳帧：双脚从接触变为不接触的第一帧
        contact_changes = np.diff(both_feet_contact.astype(int))
        takeoff_candidates = np.where(contact_changes == -1)[0] + 1
        
        # 找到落地帧：双脚从不接触变为接触的第一帧
        landing_candidates = np.where(contact_changes == 1)[0] + 1
        
        if len(takeoff_candidates) > 0 and len(landing_candidates) > 0:
            self.takeoff_frame = takeoff_candidates[0]
            # 找到起跳后的第一个落地帧
            valid_landings = landing_candidates[landing_candidates > self.takeoff_frame]
            if len(valid_landings) > 0:
                self.landing_frame = valid_landings[0]
            else:
                self.landing_frame = None
        
        if self.takeoff_frame is not None:
            print(f"检测到起跳帧: {self.takeoff_frame}")
        if self.landing_frame is not None:
            print(f"检测到落地帧: {self.landing_frame}")
            airtime = (self.landing_frame - self.takeoff_frame) / self.fps
            print(f"滞空时间: {airtime:.2f}秒")
    
    def analyze_center_of_mass(self):
        """
        分析重心运动轨迹
        
        返回:
        com_trajectory: 重心轨迹 (x, y)
        """
        print("正在分析重心运动轨迹...")
        
        # 使用髋部中点作为重心近似
        left_hip = self.smoothed_data[:, self.keypoint_indices['left_hip'], :2]
        right_hip = self.smoothed_data[:, self.keypoint_indices['right_hip'], :2]
        
        com_trajectory = (left_hip + right_hip) / 2
        
        if self.takeoff_frame is not None and self.landing_frame is not None:
            # 分析腾空阶段的重心轨迹
            airborne_com = com_trajectory[self.takeoff_frame:self.landing_frame+1]
            
            # 计算腾空高度
            takeoff_height = com_trajectory[self.takeoff_frame, 1]
            max_height = np.min(airborne_com[:, 1])  # Y值越小位置越高
            airborne_height = takeoff_height - max_height
            
            # 计算水平位移
            horizontal_distance = (com_trajectory[self.landing_frame, 0] - 
                                 com_trajectory[self.takeoff_frame, 0])
            
            print(f"腾空高度: {airborne_height:.2f} 像素")
            print(f"水平位移: {horizontal_distance:.2f} 像素")
        
        return com_trajectory
    
    def analyze_arm_movement(self):
        """
        分析手臂动作
        
        返回:
        arm_analysis: 手臂分析结果字典
        """
        print("正在分析手臂动作...")
        
        if self.takeoff_frame is None or self.landing_frame is None:
            return None
        
        # 获取手臂关键点
        left_shoulder = self.smoothed_data[:, self.keypoint_indices['left_shoulder'], :2]
        right_shoulder = self.smoothed_data[:, self.keypoint_indices['right_shoulder'], :2]
        left_wrist = self.smoothed_data[:, self.keypoint_indices['left_wrist'], :2]
        right_wrist = self.smoothed_data[:, self.keypoint_indices['right_wrist'], :2]
        
        # 计算腾空阶段手腕的轨迹
        airborne_frames = range(self.takeoff_frame, self.landing_frame + 1)
        
        left_wrist_airborne = left_wrist[airborne_frames]
        right_wrist_airborne = right_wrist[airborne_frames]
        
        # 计算手臂举起高度（相对于起跳时刻）
        left_arm_lift = (left_wrist[self.takeoff_frame, 1] - 
                        np.min(left_wrist_airborne[:, 1]))
        right_arm_lift = (right_wrist[self.takeoff_frame, 1] - 
                         np.min(right_wrist_airborne[:, 1]))
        
        print(f"左臂最大举起高度: {left_arm_lift:.2f} 像素")
        print(f"右臂最大举起高度: {right_arm_lift:.2f} 像素")
        
        return {
            'left_arm_lift': left_arm_lift,
            'right_arm_lift': right_arm_lift,
            'left_wrist_trajectory': left_wrist_airborne,
            'right_wrist_trajectory': right_wrist_airborne
        }
    
    def calculate_joint_angle(self, point1, point2, point3):
        """
        计算三点形成的关节角度
        
        参数:
        point1, point2, point3: 三个点的坐标，point2为关节点
        
        返回:
        角度（度）
        """
        vec1 = point1 - point2
        vec2 = point3 - point2
        
        cos_angle = np.sum(vec1 * vec2, axis=1) / (
            np.linalg.norm(vec1, axis=1) * np.linalg.norm(vec2, axis=1)
        )
        
        # 处理数值误差
        cos_angle = np.clip(cos_angle, -1, 1)
        angles = np.arccos(cos_angle) * 180 / np.pi
        
        return angles
    
    def analyze_leg_posture(self):
        """
        分析腿部和躯干姿态
        
        返回:
        leg_analysis: 腿部分析结果字典
        """
        print("正在分析腿部和躯干姿态...")
        
        if self.takeoff_frame is None or self.landing_frame is None:
            return None
        
        # 获取腿部关键点
        left_hip = self.smoothed_data[:, self.keypoint_indices['left_hip'], :2]
        right_hip = self.smoothed_data[:, self.keypoint_indices['right_hip'], :2]
        left_knee = self.smoothed_data[:, self.keypoint_indices['left_knee'], :2]
        right_knee = self.smoothed_data[:, self.keypoint_indices['right_knee'], :2]
        left_ankle = self.smoothed_data[:, self.keypoint_indices['left_ankle'], :2]
        right_ankle = self.smoothed_data[:, self.keypoint_indices['right_ankle'], :2]
        
        # 计算膝关节角度
        left_knee_angles = self.calculate_joint_angle(left_hip, left_knee, left_ankle)
        right_knee_angles = self.calculate_joint_angle(right_hip, right_knee, right_ankle)
        
        # 腾空阶段分析
        airborne_frames = range(self.takeoff_frame, self.landing_frame + 1)
        
        left_knee_airborne = left_knee_angles[airborne_frames]
        right_knee_airborne = right_knee_angles[airborne_frames]
        
        # 找到最大屈膝角度（最小角度值）
        min_left_knee_angle = np.min(left_knee_airborne)
        min_right_knee_angle = np.min(right_knee_airborne)
        
        # 落地时的膝关节角度
        landing_left_knee = left_knee_angles[self.landing_frame]
        landing_right_knee = right_knee_angles[self.landing_frame]
        
        print(f"腾空期最大屈膝角度 - 左腿: {min_left_knee_angle:.1f}°, 右腿: {min_right_knee_angle:.1f}°")
        print(f"落地时膝关节角度 - 左腿: {landing_left_knee:.1f}°, 右腿: {landing_right_knee:.1f}°")
        
        return {
            'left_knee_angles': left_knee_angles,
            'right_knee_angles': right_knee_angles,
            'min_knee_flexion': (min_left_knee_angle + min_right_knee_angle) / 2,
            'landing_knee_angle': (landing_left_knee + landing_right_knee) / 2
        }
    
    def plot_analysis_results(self, com_trajectory, arm_analysis, leg_analysis):
        """
        绘制分析结果
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 重心轨迹
        axes[0, 0].plot(com_trajectory[:, 0], com_trajectory[:, 1], 'b-', alpha=0.7)
        if self.takeoff_frame is not None and self.landing_frame is not None:
            # 标记起跳和落地点
            axes[0, 0].plot(com_trajectory[self.takeoff_frame, 0], 
                           com_trajectory[self.takeoff_frame, 1], 'go', markersize=8, label='起跳')
            axes[0, 0].plot(com_trajectory[self.landing_frame, 0], 
                           com_trajectory[self.landing_frame, 1], 'ro', markersize=8, label='落地')
            # 腾空轨迹
            airborne_com = com_trajectory[self.takeoff_frame:self.landing_frame+1]
            axes[0, 0].plot(airborne_com[:, 0], airborne_com[:, 1], 'r-', linewidth=2, label='腾空轨迹')
        
        axes[0, 0].set_title('重心运动轨迹')
        axes[0, 0].set_xlabel('X坐标 (像素)')
        axes[0, 0].set_ylabel('Y坐标 (像素)')
        axes[0, 0].legend()
        axes[0, 0].invert_yaxis()  # 翻转Y轴以匹配图像坐标系
        
        # 2. 脚部接触状态
        left_contact, right_contact = self.detect_foot_contact()
        frames = np.arange(len(left_contact))
        
        axes[0, 1].fill_between(frames, 0, left_contact.astype(int), alpha=0.3, label='左脚接触', color='blue')
        axes[0, 1].fill_between(frames, 1, 1 + right_contact.astype(int), alpha=0.3, label='右脚接触', color='red')
        
        if self.takeoff_frame is not None:
            axes[0, 1].axvline(x=self.takeoff_frame, color='green', linestyle='--', label='起跳')
        if self.landing_frame is not None:
            axes[0, 1].axvline(x=self.landing_frame, color='red', linestyle='--', label='落地')
        
        axes[0, 1].set_title('脚部接触状态')
        axes[0, 1].set_xlabel('帧数')
        axes[0, 1].set_ylabel('接触状态')
        axes[0, 1].set_yticks([0.5, 1.5])
        axes[0, 1].set_yticklabels(['左脚', '右脚'])
        axes[0, 1].legend()
        
        # 3. 手臂轨迹（如果有数据）
        if arm_analysis is not None:
            left_wrist = arm_analysis['left_wrist_trajectory']
            right_wrist = arm_analysis['right_wrist_trajectory']
            
            axes[1, 0].plot(left_wrist[:, 0], left_wrist[:, 1], 'b-', label='左手腕', linewidth=2)
            axes[1, 0].plot(right_wrist[:, 0], right_wrist[:, 1], 'r-', label='右手腕', linewidth=2)
        
        axes[1, 0].set_title('腾空期手腕轨迹')
        axes[1, 0].set_xlabel('X坐标 (像素)')
        axes[1, 0].set_ylabel('Y坐标 (像素)')
        axes[1, 0].legend()
        axes[1, 0].invert_yaxis()
        
        # 4. 膝关节角度变化（如果有数据）
        if leg_analysis is not None:
            frames = np.arange(len(leg_analysis['left_knee_angles']))
            axes[1, 1].plot(frames, leg_analysis['left_knee_angles'], 'b-', label='左膝角度', linewidth=2)
            axes[1, 1].plot(frames, leg_analysis['right_knee_angles'], 'r-', label='右膝角度', linewidth=2)
            
            if self.takeoff_frame is not None:
                axes[1, 1].axvline(x=self.takeoff_frame, color='green', linestyle='--', label='起跳')
            if self.landing_frame is not None:
                axes[1, 1].axvline(x=self.landing_frame, color='red', linestyle='--', label='落地')
        
        axes[1, 1].set_title('膝关节角度变化')
        axes[1, 1].set_xlabel('帧数')
        axes[1, 1].set_ylabel('角度 (度)')
        axes[1, 1].legend()
        
        plt.tight_layout()
        plt.show()
    
    def run_analysis(self):
        """
        运行完整分析流程
        """
        print("开始立定跳远动作分析...")
        print("=" * 50)
        
        # 1. 数据预处理
        self.smooth_keypoints()
        
        # 2. 建立地面基准
        self.establish_ground_baseline()
        
        # 3. 检测脚部接触状态
        left_contact, right_contact = self.detect_foot_contact()
        
        # 4. 识别起跳和落地帧
        self.detect_takeoff_landing(left_contact, right_contact)
        
        if self.takeoff_frame is None or self.landing_frame is None:
            print("警告: 无法检测到完整的起跳-落地过程")
            return None
        
        print("\n" + "=" * 50)
        print("滞空阶段身体运动分析:")
        print("=" * 50)
        
        # 5. 重心轨迹分析
        com_trajectory = self.analyze_center_of_mass()
        
        # 6. 手臂动作分析
        arm_analysis = self.analyze_arm_movement()
        
        # 7. 腿部姿态分析
        leg_analysis = self.analyze_leg_posture()
        
        # 8. 绘制结果
        self.plot_analysis_results(com_trajectory, arm_analysis, leg_analysis)
        
        print("\n" + "=" * 50)
        print("分析完成!")
        
        return {
            'takeoff_frame': self.takeoff_frame,
            'landing_frame': self.landing_frame,
            'com_trajectory': com_trajectory,
            'arm_analysis': arm_analysis,
            'leg_analysis': leg_analysis
        }

# 使用示例 - 针对您的数据格式
def analyze_jump_from_excel(file_path):
    """
    从Excel文件分析立定跳远动作
    
    参数:
    file_path: Excel文件路径
    """
    print("立定跳远动作分析程序")
    print("=" * 60)
    
    try:
        # 创建分析器实例，自动加载Excel数据
        analyzer = JumpAnalyzer(data_path=file_path, fps=30)  # 300帧10秒，fps=30
        
        # 运行完整分析
        results = analyzer.run_analysis()
        
        if results is not None:
            print("\n" + "=" * 60)
            print("分析结果汇总:")
            print("=" * 60)
            print(f"起跳帧: {results['takeoff_frame']}")
            print(f"落地帧: {results['landing_frame']}")
            
            if results['takeoff_frame'] is not None and results['landing_frame'] is not None:
                airtime = (results['landing_frame'] - results['takeoff_frame']) / 30
                print(f"滞空时间: {airtime:.2f} 秒")
                print(f"滞空帧数: {results['landing_frame'] - results['takeoff_frame']} 帧")
        
        return results
        
    except Exception as e:
        print(f"分析过程中出现错误: {str(e)}")
        print("请检查数据文件格式是否正确")
        return None

# 针对您的具体文件路径
if __name__ == "__main__":
    # 您的数据文件路径
    data_file = r"C:\Users\<USER>\Desktop\2025E\问题1\运动者1的跳远位置信息.xlsx"
    
    # 检查是否是目录，如果是目录，需要指定具体文件名
    if os.path.isdir(data_file):
        print(f"指定的路径是目录: {data_file}")
        print("请在目录中找到具体的.xlsx文件")
        
        # 列出目录中的xlsx文件
        xlsx_files = [f for f in os.listdir(data_file) if f.endswith('.xlsx')]
        if xlsx_files:
            print("找到以下xlsx文件:")
            for i, file in enumerate(xlsx_files):
                print(f"{i+1}. {file}")
            
            # 如果只有一个文件，自动使用
            if len(xlsx_files) == 1:
                data_file = os.path.join(data_file, xlsx_files[0])
                print(f"自动选择文件: {data_file}")
            else:
                print("请手动指定完整的文件路径，包含文件名")
                exit()
        else:
            print("目录中没有找到xlsx文件")
            exit()
    
    # 运行分析
    if os.path.isfile(data_file) and data_file.endswith('.xlsx'):
        results = analyze_jump_from_excel(data_file)
    else:
        print("请提供有效的xlsx文件路径")